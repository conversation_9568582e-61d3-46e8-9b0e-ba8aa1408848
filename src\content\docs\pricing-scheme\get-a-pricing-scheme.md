---
title: Get a pricing scheme
description: Get a pricing scheme endpoint documentation.
---


Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/pricing-schemes/{pricingSchemeId}
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter       | Type          | Required | Description                   |
|-----------------|---------------|----------|-------------------------------|
| companyId       | string <uuid> | yes      | Your inFlow account companyId |
| pricingSchemeId | string <uuid> | yes      | The pricingSchemeId to be fetched |

### Response

#### Success response (200) schema: `application/json`

| Field           | Type                | Description |
|-----------------|---------------------|-------------|
| currency        | object (Currency)   | The currency this pricing scheme is based on |
| currencyId      | string <uuid>       | The currencyId this pricing scheme is based on |
| isActive        | boolean             | Pricing schemes with `IsActive = false` are deactivated and hidden away for new usage. |
| isDefault       | boolean             | Only one pricing scheme, your company-wide default, should have `IsDefault = true`. |
| isTaxInclusive  | boolean             | Set `IsTaxInclusive` if prices should be interpreted as tax-inclusive, otherwise tax will be added on top of listed prices. |
| name            | string              | Human-readable name for this pricing scheme. Not shown to customers on invoices, etc. |
| pricingSchemeId | string <uuid>       | The primary identifier for this pricing scheme. Not shown to users |
| productPrices   | Array of objects    | The list of prices under this pricing scheme. |
| timestamp       | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 1186 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-get-a-pricing-scheme.json](response-sample-of-get-a-pricing-scheme.json)
