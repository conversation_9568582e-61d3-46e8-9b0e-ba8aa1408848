---
title: Insert or update product cost adjustment
description: Insert or update product cost adjustment endpoint documentation.
---


### Request endpoint

```http
PUT /{companyId}/product-cost-adjustments
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

A product cost adjustment to insert or update

`productCostAdjustmentId` property is required, please generate a GUID when inserting.

| Field                     | Type                     | Description   |
|---------------------------|--------------------------|---------------|
| dateTime                  | string <date-time>       | The effective date of this adjustment |
| lastModifiedBy            | object (TeamMember)      | The inFlow Team Member, system process, or API key that last modified this product cost adjustment. This is set automatically, and cannot be set through the API. |
| lastModifiedById          | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this product cost adjustment. This is set automatically, and cannot be set through the API. |
| product                   | object (Product)         | The product whose cost is being adjusted |
| productCostAdjustmentId   | string <uuid>            | The primary identifier for this product cost adjustment. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| productId                 | string <uuid>            | The primary identifier for this product. |
| serial                    | string                   | For serialized products, the serial number whose cost is being adjusted. |
| timestamp                 | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| unitCost                  | string <decimal>         | The new target inventory cost per standard unit of measure |

### Payload example

> **WARNING**: The payload sample is 1253 lines long.
> Please follow the link below to see payload JSON.

[payload-sample-of-insert-or-update-product-cost-adjustment.json](payload-sample-of-insert-or-update-product-cost-adjustment.json)

### Response

#### Success response (200) schema: `application/json`

| Field                     | Type                     | Description   |
|---------------------------|--------------------------|---------------|
| dateTime                  | string <date-time>       | The effective date of this adjustment |
| lastModifiedBy            | object (TeamMember)      | The inFlow Team Member, system process, or API key that last modified this product cost adjustment. This is set automatically, and cannot be set through the API. |
| lastModifiedById          | string <uuid>            | The inFlow Team Member, system process, or API key that last modified this product cost adjustment. This is set automatically, and cannot be set through the API. |
| product                   | object (Product)         | The product whose cost is being adjusted |
| productCostAdjustmentId   | string <uuid>            | The primary identifier for this product cost adjustment. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| productId                 | string <uuid>            | The primary identifier for this product. |
| serial                    | string                   | For serialized products, the serial number whose cost is being adjusted. |
| timestamp                 | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| unitCost                  | string <decimal>         | The new target inventory cost per standard unit of measure |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 1253 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-insert-or-update-product-cost-adjustment.json](response-sample-of-insert-or-update-product-cost-adjustment.json)
