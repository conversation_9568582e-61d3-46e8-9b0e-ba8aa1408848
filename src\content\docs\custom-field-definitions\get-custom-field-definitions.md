---
title: Get custom field definitions
description: Get custom field definitions endpoint documentation.
---

### Request endpoint

```http
GET /{companyId}/custom-field-definitions
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of `CustomFieldDefinition` objects. Each `CustomFieldDefinition` object has the following properties:

| Field           | Type                   | Description               |
|-----------------|------------------------|---------------------------|
| categoryId      | string <uuid> Nullable |                           |
| customFieldDefinitionId | string         | The primary identifier for this custom field definition. [When inserting, you can insert any value here, it will be ignored](/overview/#rite-requests). Not shown to users |
| customFieldType | string of enum         | The type of custom field and how it will be displayed to the user |
| entityType      | string of enum         | The entity type of the custom field, e.g. `product` |
| isActive        | boolean                | Whether this custom field is active and should be displayed |
| label           | string                 | Human-readable name for this custom field. |
| propertyName    | string                 | The property name for the custom field, should be one of custom1 through custom10 |

Available enum string values for `customFieldType`:
- "Text"
- "Dropdown"
- "Date"
- "Checkbox"

Available enum string values for `entityType`:
- "SalesOrder"
- "PurchaseOrder"
- "Product"
- "Vendor"
- "Customer"
- "CountSheet"
- "StockTransfer"
- "StockAdjustment"
- "StockCount"
- "ManufacturingOrder"

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "category": {
      "categoryId": "00000000-0000-0000-0000-000000000000",
      "isDefault": true,
      "name": "Bestsellers",
      "parentCategory": {},
      "parentCategoryId": "00000000-0000-0000-0000-000000000000",
      "timestamp": "0000000000310AB6"
    },
    "categoryId": "00000000-0000-0000-0000-000000000000",
    "customFieldDefinitionId": "string",
    "customFieldType": "Text",
    "entityType": "SalesOrder",
    "isActive": true,
    "label": "string",
    "propertyName": "string"
  }
]
```
