---
title: Set custom field dropdown options
description: Set custom field dropdown options endpoint documentation.
---

### Request endpoint

```http
PUT /{companyId}/custom-field-dropdown-options
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

| Field         | Type              | Description |
|---------------|-------------------|-------------|
| attributes    | object (Nullable) |             |
| relationships | object (Nullable) |             |
| meta          | object (Nullable) |             |

### Payload example

```json
{
  "attributes": {
    "property1": {},
    "property2": {}
  },
  "relationships": {
    "property1": [
      {
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ],
    "property2": [
      {
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ]
  },
  "meta": {
    "property1": {},
    "property2": {}
  }
}
```

### Response

#### Success response (200) schema: `application/json`

| Field | Type | Description |
|-------|------|-------------|
| dropdownOptions | Array of strings               | The list of valid options for this custom field |
| entityType     | string (Enum: "SalesOrder", "PurchaseOrder", "Product", "Vendor", "Customer", "CountSheet", "StockTransfer", "StockAdjustment", "StockCount", "ManufacturingOrder") | The entity type (e.g. Product) that this custom field applies to |
| id             | string                         | Ignored     |
| propertyName   | string                         | The property name of the custom field. Should be one of Custom1, Custom2, ... Custom10 |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "dropdownOptions": [
    "string"
  ],
  "entityType": "SalesOrder",
  "id": "string",
  "propertyName": "string"
}
```
