---
title: List currencies
description: List currencies endpoint documentation.
---

### Request endpoint

```http
GET /{companyId}/currencies
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of `Currency` objects. Each `Currency` object has the following properties:

| Field          | Type              | Description                     |
|----------------|-------------------|---------------------------------|
| currencyConversions | `Array of objects` | A list of conversion rates related to this currency. |
| currencyId     | `string <uuid>`   | The primary identifier for this currency. Not shown to users |
| decimalPlaces  | `integer <int32>` | The number of decimal places typically shown with this currency |
| decimalSeparator | `string`        | The symbol used to separate decimals with this currency |
| isSymbolFirst  | `boolean`         | Whether the symbol is shown prior to the numerical value for this currency |
| isoCode        | `string`          | The ISO 4217 code for this currency |
| name           | `string`          | A descriptive name of this currency |
| negativeType   | `string` of enum  | How negative numbers are shown for this currency |
| symbol         | `string`          | A short symbol representing this currency |
| thousandsSeparator | `string`      | The symbol used to separate thousands with this currency |
| timestamp      | `string <rowversion>` | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

Valid values for `negativeType`:

- `Leading`
- `LeadingInsideSymbol`
- `TrailingInsideSymbol`
- `Trailing`
- `Bracketed`

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "currencyConversions": [
      {
        "currency": {},
        "currencyConversionId": "string",
        "currencyId": "00000000-0000-0000-0000-000000000000",
        "exchangeRate": "1.29",
        "isManual": true,
        "timestamp": "0000000000310AB6"
      }
    ],
    "currencyId": "00000000-0000-0000-0000-000000000000",
    "decimalPlaces": 2,
    "decimalSeparator": ".",
    "isSymbolFirst": true,
    "isoCode": "USD",
    "name": "US Dollar",
    "negativeType": "Leading",
    "symbol": "$",
    "thousandsSeparator": ",",
    "timestamp": "0000000000310AB6"
  }
]
