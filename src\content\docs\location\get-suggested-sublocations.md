---
title: Get suggested sublocations
description: Get suggested sublocations endpoint documentation.
---


Get a list of known sublocations within this location. Sublocations are typically represented just as a string, and do not need to be registered/created in advance.

### Request endpoint

```http
GET /{companyId}/locations/{locationId}/suggested-sublocations
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter  | Type          | Required | Description                   |
|------------|---------------|----------|-------------------------------|
| companyId  | string <uuid> | yes      | Your inFlow account companyId |
| locationId | string <uuid> | yes      | Id of the location to fetch suggested sublocations for |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

| Field         | Type              | Description |
|---------------|-------------------|-------------|
| attributes    | object (Nullable) |             |
| relationships | object (Nullable) |             |
| meta          | object (Nullable) |             |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "attributes": {
    "property1": {},
    "property2": {}
  },
  "relationships": {
    "property1": [
      {
        "attributes": {
          "property1": {},
          "property2": {}
        },
        "relationships": {
          "property1": [
            {}
          ],
          "property2": [
            {}
          ]
        },
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ],
    "property2": [
      {
        "attributes": {
          "property1": {},
          "property2": {}
        },
        "relationships": {
          "property1": [
            {}
          ],
          "property2": [
            {}
          ]
        },
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ]
  },
  "meta": {
    "property1": {},
    "property2": {}
  }
}
```
