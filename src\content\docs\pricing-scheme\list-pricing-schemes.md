---
title: List pricing schemes
description: List pricing schemes endpoint documentation.
---


Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/pricing-schemes
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of `PricingScheme` objects. Each `PricingScheme` object has the following properties:

| Field           | Type                | Description             |
|-----------------|---------------------|-------------------------|
| currency        | object (Currency)   |                         |
| currencyId      | string <uuid>       |                         |
| isActive        | boolean             | Pricing schemes with `IsActive = false` are deactivated and hidden away for new usage. |
| isDefault       | boolean             | Only one pricing scheme, your company-wide default, should have `IsDefault = true`. |
| isTaxInclusive  | boolean             | Set `IsTaxInclusive` if prices should be interpreted as tax-inclusive, otherwise tax will be added on top of listed prices. |
| name            | string              | Human-readable name for this pricing scheme. Not shown to customers on invoices, etc. |
| pricingSchemeId | string <uuid>       | The primary identifier for this pricing scheme. Not shown to users |
| productPrices   | Array of objects    | The list of prices under this pricing scheme. |
| timestamp       | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 1186 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-list-pricing-schemes.json](response-sample-of-list-pricing-schemes.json)
