---
title: List team members in your inFlow account.
description: List team members in your inFlow account. endpoint documentation.
---


This only includes inFlow users, not other types (e.g. API keys) that can be returned by `LastModifiedBy`.

### Request endpoint

```http
GET /{companyId}/team-members
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of TeamMember objects. Each TeamMember object has the following properties:

| Field               | Type                | Description              |
|---------------------|---------------------|--------------------------|
| accessAllLocations  | boolean             | Whether this team member has access to all locations |
| accessLocationIds   | Array of strings <uuid> | If CanAccessAllLocations is false, then the list of Location ids that this team member has access to |
| accessRights        | Array of enum strings | A list of access rights given to this team member |
| canBeSalesRep       | boolean             | Whether this team member should be included as a choice for sales rep |
| email               | string              | The email address associated with this user |
| isActive            | boolean             | Whether this user is currently an active inFlow user |
| isInternal          | boolean             | Whether this user is an internal inFlow system account |
| name                | string              | The name of this inFlow user |
| teamMemberId        | string <uuid>       | The primary identifier for this user. Not shown to users |

### Available enums for `accessRights` property:
```
"SALES_SalesOrder_View"
"SALES_SalesOrder_Edit"
"SALES_SalesOrder_Pick"
"SALES_SalesOrder_Approve"
"SALES_SalesOrder_Prioritization"
"SALES_Customer_View"
"SALES_Customer_Edit"
"SALES_SalesPrices_Edit"
"PUR_PurchaseOrder_View"
"PUR_PurchaseOrder_Edit"
"PUR_PurchaseOrder_Receive"
"PUR_Vendor_View"
"PUR_Vendor_Edit"
"INV_ReorderStock_Edit"
"INV_CountSheet_View"
"INV_CountSheet_Edit"
"INV_CountSheet_Only"
"INV_TransferStock_View"
"INV_TransferStock_Edit"
"INV_TransferStock_Approve"
"INV_AdjustStock_View"
"INV_AdjustStock_Edit"
"INV_CurrentStock_View"
"INV_History_View"
"INV_Product_View"
"INV_Product_Edit"
"INV_CostInfo_View"
"INV_CostInfo_Edit"
"INV_ProductCategory_Edit"
"INV_WorkOrder_View"
"INV_WorkOrder_Edit"
"INV_WorkOrder_Prioritization"
"INV_StockroomScan_View"
"INV_StockroomScan_Edit"
"SETG_Settings_View"
"SETG_Settings_Edit"
"SETG_Import_Edit"
"SETG_Export_Edit"
"SETG_Backup_Edit"
"SETG_PrintSettings_View"
"SETG_PrintSettings_Edit"
"SETG_RestoreReset_Edit"
"SETG_Integration_Edit"
"RPT_Reports_View"
```

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "accessAllLocations": true,
    "accessLocationIds": [
      "00000000-0000-0000-0000-000000000000"
    ],
    "accessRights": [
      "SALES_SalesOrder_View"
    ],
    "canBeSalesRep": true,
    "email": "string",
    "isActive": true,
    "isInternal": true,
    "name": "John Doe",
    "teamMemberId": "00000000-0000-0000-0000-000000000000"
  }
]
```
