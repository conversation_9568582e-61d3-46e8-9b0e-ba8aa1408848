---
title: List taxing schemes
description: List taxing schemes endpoint documentation.
---


Relationships can be included via the `include` query parameter.

Options for filtering this list:
- `filter[name]`

### Request endpoint

```http
GET /{companyId}/taxing-schemes
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of TaxingScheme Objects. Each TaxingScheme object has the following properties:

| Field               | Type                | Description              |
|---------------------|---------------------|--------------------------|
| calculateTax2OnTax1 | boolean             | Whether a secondary tax should be compounded on top of the primary tax |
| defaultTaxCode      | object (TaxCode)    |                          |
| defaultTaxCodeId    | string <uuid>       |                          |
| isActive            | boolean             | Taxing schemes with `IsActive = false` are deactivated and hidden away for new usage. |
| isDefault           | boolean             | Only one taxing scheme, your company-wide default, should have `IsDefault = true`. |
| name                | string              | Human-readable name for this pricing scheme. Not shown to customers on invoices, etc. |
| tax1Name            | string              | A short name for display of the primary tax |
| tax1OnShipping      | boolean             | Whether the primary tax applies to shipping/freight costs |
| tax2Name            | string              | A short name for display of the secondary tax |
| tax2OnShipping      | boolean             | Whether the secondary tax applies to shipping/freight costs |
| taxCodes            | Array of objects    | A list of other potential tax codes (percentages) for this taxing scheme, e.g. for tax-exempt items |
| taxingSchemeId      | string <uuid>       | The primary identifier for this taxing scheme. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| timestamp           | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "calculateTax2OnTax1": true,
    "defaultTaxCode": {
      "isActive": true,
      "name": "Taxable",
      "tax1Rate": "19.99",
      "tax2Rate": "19.99",
      "taxCodeId": "00000000-0000-0000-0000-000000000000",
      "taxingScheme": {},
      "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
      "timestamp": "0000000000310AB6"
    },
    "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000",
    "isActive": true,
    "isDefault": true,
    "name": "NYC sales tax",
    "tax1Name": "VAT",
    "tax1OnShipping": true,
    "tax2Name": "PST",
    "tax2OnShipping": true,
    "taxCodes": [
      {
        "isActive": true,
        "name": "Taxable",
        "tax1Rate": "19.99",
        "tax2Rate": "19.99",
        "taxCodeId": "00000000-0000-0000-0000-000000000000",
        "taxingScheme": {},
        "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
        "timestamp": "0000000000310AB6"
      }
    ],
    "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
    "timestamp": "0000000000310AB6"
  }
]
```
