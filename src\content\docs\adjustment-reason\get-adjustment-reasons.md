---
title: Get adjustment reasons
description: Relationships can be included via the `include` query parameter.
---

Relationships can be included via the `include` query parameter.

### Request endpoint

```
GET /{companyId}/adjustment-reasons/{adjustmentReasonId}
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter          | Type          | Required | Description                   |
|--------------------|---------------|----------|-------------------------------|
| companyId          | string <uuid> | yes      | Your inFlow account companyId |
| adjustmentReasonId | string <uuid> | yes      | Id of the AdjustmentReason to retrieve |

### Response

#### Success response (200) schema: `application/json`

| Field         | Type              | Description |
|---------------|-------------------|-------------|
| attributes    | object (Nullable) |             |
| relationships | object (Nullable) |             |
| meta          | object (Nullable) |             |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "attributes": {
    "property1": {},
    "property2": {}
  },
  "relationships": {
    "property1": [
      {
        "attributes": {
          "property1": {},
          "property2": {}
        },
        "relationships": {
          "property1": [{}],
          "property2": [{}]
        },
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ],
    "property2": [
      {
        "attributes": {
          "property1": {},
          "property2": {}
        },
        "relationships": {
          "property1": [{}],
          "property2": [{}]
        },
        "meta": {
          "property1": {},
          "property2": {}
        }
      }
    ]
  },
  "meta": {
    "property1": {},
    "property2": {}
  }
}
```
