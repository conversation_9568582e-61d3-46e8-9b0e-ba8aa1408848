---
title: Get a product cost adjustment
description: Get a product cost adjustment endpoint documentation.
---


Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/product-cost-adjustments/{productCostAdjustmentId}
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |
| productCostAdjustmentId | string <uuid> | yes | The productCostAdjustmentId to be fetched |

### Response

#### Success response (200) schema: `application/json`

| Field            | Type                | Description                 |
|------------------|---------------------|-----------------------------|
| dateTime         | string <date-time>  | The effective date of this adjustment |
| lastModifiedBy   | object (TeamMember) |                             |
| lastModifiedById | string <uuid>       | The inFlow Team Member, system process, or API key that last modified this product cost adjustment. This is set automatically, and cannot be set through the API. |
| product          | object (Product)    |                             |
| productCostAdjustmentId | string <uuid> | The primary identifier for this product cost adjustment. [When inserting, you should specify this by generating a GUID](/overview/#Write-requests). Not shown to users |
| productId        | string <uuid>       |                             |
| serial           | string              | For serialized products, the serial number whose cost is being adjusted. |
| timestamp        | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| unitCost         | string <decimal>    | The new target inventory cost per standard unit of measure |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 1253 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-get-a-product-cost-adjustment.json](response-sample-of-get-a-product-cost-adjustment.json)
